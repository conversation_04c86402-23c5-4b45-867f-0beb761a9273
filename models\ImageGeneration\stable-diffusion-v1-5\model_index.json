{"_class_name": "StableDiffusionPipeline", "_diffusers_version": "0.6.0", "feature_extractor": ["transformers", "CLIPImageProcessor"], "safety_checker": ["stable_diffusion", "StableDiffusionSafetyChecker"], "scheduler": ["diffusers", "PNDMScheduler"], "text_encoder": ["transformers", "CLIPTextModel"], "tokenizer": ["transformers", "CLIPTokenizer"], "unet": ["diffusers", "UNet2DConditionModel"], "vae": ["diffusers", "AutoencoderKL"]}