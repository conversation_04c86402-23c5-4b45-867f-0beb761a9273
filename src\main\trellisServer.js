const { spawn } = require('child_process');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const net = require('net');
const path = require('path');
const chalk = require('chalk');
chalk.level = 3; // Force 16m color support for blue logs

const logTrellisServer = (...args) => {
  const prefix = chalk.blue('[Trellis Server]');
  if (typeof logger !== 'undefined' && logger.info) {
    logger.info(prefix, ...args);
  }
  console.log(prefix, ...args);
};

const TRELLIS_PORT = 7960;
const TRELLIS_HOST = '127.0.0.1';
const RUN_BAT = path.join(__dirname, '../../pipelines/3DPipelines/gen3d/trellis-stable-projectorz-101/run.bat');
const OUTPUT_DIR = path.join(__dirname, '../../output');

// Connection attempt counter to reduce log spam
let connectionAttemptCounter = 0;
let recoveryAttemptCounter = 0;
const MAX_RECOVERY_ATTEMPTS = 5; // Increased from 3 to 5
const CONNECTION_ATTEMPTS_BEFORE_RECOVERY = 200; // Increased from 80 to 200

// Track server initialization failures for automatic reset
let initializationFailureDetected = false;

// Automated Python process cleanup function
async function cleanupPythonProcesses() {
  logTrellisServer('Checking for lingering Python processes...');

  return new Promise((resolve) => {
    // First, check if there are any Python processes running
    const checkProcess = spawn('cmd.exe', ['/c', 'tasklist | findstr python.exe'], {
      stdio: 'pipe',
      windowsHide: true
    });

    let output = '';
    checkProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    checkProcess.on('close', (code) => {
      if (output.trim() && output.includes('python.exe')) {
        logTrellisServer('Found Python processes, killing them...');
        logTrellisServer('Processes found:', output.trim());

        // Kill all Python processes
        const killProcess = spawn('cmd.exe', ['/c', 'taskkill /F /IM python.exe'], {
          stdio: 'pipe',
          windowsHide: true
        });

        killProcess.on('close', (killCode) => {
          if (killCode === 0) {
            logTrellisServer('Successfully killed Python processes');
          } else {
            logTrellisServer('Some Python processes may still be running (exit code:', killCode, ')');
          }

          // Wait a moment for processes to fully terminate
          setTimeout(() => {
            resolve();
          }, 2000);
        });

        killProcess.on('error', (err) => {
          logTrellisServer('Error killing Python processes:', err.message);
          resolve();
        });
      } else {
        logTrellisServer('No Python processes found');
        resolve();
      }
    });

    checkProcess.on('error', (err) => {
      logTrellisServer('Error checking for Python processes:', err.message);
      resolve();
    });
  });
}

function isTrellisRunning() {
  // No longer log routine connection checks - they're just noise since server works fine

  return new Promise((resolve) => {
    const socket = net.createConnection(TRELLIS_PORT, TRELLIS_HOST);
    socket.on('connect', () => {
      // Only log successful connection if we had previous failures
      if (connectionAttemptCounter > 0) {
        logTrellisServer('Server is running - connection successful');
      }
      connectionAttemptCounter = 0; // Reset counter on successful connection
      socket.end();
      resolve(true);
    });
    socket.on('error', (error) => {
      connectionAttemptCounter++;

      // Don't log routine ECONNREFUSED errors - they're expected when server is starting
      // Only log significant events like auto-recovery

      // Auto-recovery: If we've failed many times, try to restart the server
      if (connectionAttemptCounter >= CONNECTION_ATTEMPTS_BEFORE_RECOVERY && recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
        logTrellisServer('🔄 Auto-recovery triggered after ' + CONNECTION_ATTEMPTS_BEFORE_RECOVERY + ' failed attempts (attempt ' + (recoveryAttemptCounter + 1) + '/' + MAX_RECOVERY_ATTEMPTS + ')');
        triggerServerRestart(globalProgressCallback);
      } else if (connectionAttemptCounter >= CONNECTION_ATTEMPTS_BEFORE_RECOVERY && recoveryAttemptCounter >= MAX_RECOVERY_ATTEMPTS) {
        logTrellisServer('❌ Max recovery attempts (' + MAX_RECOVERY_ATTEMPTS + ') reached. Server may be permanently stuck.');
      }

      resolve(false);
    });
  });
}

function startTrellisServer(progressCb = null) {
  logTrellisServer('Starting server...');
  logTrellisServer('RUN_BAT:', RUN_BAT);
  logTrellisServer('Batch file exists:', fs.existsSync(RUN_BAT));

  if (!fs.existsSync(RUN_BAT)) {
    throw new Error('Trellis run.bat not found at: ' + RUN_BAT);
  }
  const batDir = path.dirname(RUN_BAT);
  logTrellisServer('Working directory:', batDir);
  logTrellisServer('Command: cmd.exe /c', path.basename(RUN_BAT));

  // Store the progress callback globally so it can be used during generation
  globalProgressCallback = progressCb;

  // Use cmd.exe with specific flags to prevent window showing
  const child = spawn('cmd.exe', ['/c', path.basename(RUN_BAT)], {
    cwd: batDir,
    detached: false,  // Changed to false so we can capture output
    stdio: ['ignore', 'pipe', 'pipe'],
    windowsHide: true,
    shell: false,
    env: {
      ...process.env,
      // Set environment variables to minimize window visibility
      PYTHONUNBUFFERED: '1'
    }
  });
  // Capture and display selective server output in main application logs
  child.stdout.on('data', (data) => {
    const output = data.toString();
    const lines = output.split('\n').filter(line => line.trim());

    lines.forEach(line => {
      const trimmedLine = line.trim();

      // Debug: Log all progress lines to see what we're getting (temporarily enabled)
      if (trimmedLine.includes('Sampling:') || trimmedLine.includes('Decimating') ||
          trimmedLine.includes('Rendering:') || trimmedLine.includes('Rasterizing:') ||
          trimmedLine.includes('Texture baking')) {
        logTrellisServer('Raw line:', trimmedLine);
      }

      // Check if this is a progress line that should be logged
      const isProgressLine =
        trimmedLine.includes('Sampling:') ||
        trimmedLine.includes('Decimating') ||
        trimmedLine.includes('Rendering:') ||
        trimmedLine.includes('Rasterizing:') ||
        trimmedLine.includes('Texture baking');

      // Log all milestone progress lines (10%, 20%, 30%, etc.)
      if (isProgressLine && isMilestoneProgress(trimmedLine)) {
        logTrellisServer('[Trellis Progress] ' + trimmedLine);
        if (typeof logger !== 'undefined') logger.info('[Trellis Progress] ' + trimmedLine);
      }

      // Also log any line that contains a percentage for broader coverage
      if (trimmedLine.includes('%') && !trimmedLine.includes('ECONNREFUSED')) {
        const percentageMatch = trimmedLine.match(/(\d+)%/);
        if (percentageMatch) {
          const percentage = parseInt(percentageMatch[1]);
          // Log milestone percentages from any source
          if (percentage % 10 === 0 || percentage === 0 || percentage === 100) {
            logTrellisServer('[Trellis Progress] ' + trimmedLine);
            if (typeof logger !== 'undefined') logger.info('[Trellis Progress] ' + trimmedLine);
          }
        }
      }

      // Only log important non-progress lines
      const shouldLog =
        trimmedLine.includes('INFO') ||
        trimmedLine.includes('ERROR') ||
        trimmedLine.includes('WARNING') ||
        trimmedLine.includes('Starting') ||
        trimmedLine.includes('Completed') ||
        trimmedLine.includes('Failed') ||
        trimmedLine.includes('Loading') ||
        trimmedLine.includes('Initializing') ||
        trimmedLine.includes('Client asked to generate') ||
        trimmedLine.includes('Decoding the SLAT') ||
        trimmedLine.includes('GLB saved to') ||
        trimmedLine.includes('Generation completed') ||
        trimmedLine.includes('error while attempting to bind') ||
        trimmedLine.includes('Address already in use') ||
        trimmedLine.includes('Something went wrong');

      if (shouldLog) {
        logTrellisServer('[Trellis Server] ' + trimmedLine);
        if (typeof logger !== 'undefined') logger.info('[Trellis Server] ' + trimmedLine);
      }

      // Check for port binding errors
      if (trimmedLine.includes('error while attempting to bind') || trimmedLine.includes('Address already in use')) {
        logTrellisServer('PORT CONFLICT DETECTED - Another server is using port 7960');
        logTrellisServer('Please close any other Trellis servers and try again');
      }

      // Check for initialization failure and trigger automatic restart
      if (trimmedLine.includes('Something went wrong')) {
        if (recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
          logTrellisServer('🔄 Server initialization failed - triggering automatic restart...');
          // Use setTimeout to avoid blocking the current process cleanup
          setTimeout(() => {
            triggerServerRestart(globalProgressCallback);
          }, 1000);
        } else {
          logTrellisServer('❌ Maximum recovery attempts reached, not restarting automatically');
        }
      }
    });

    // Parse progress from server output if we have a callback (but don't log every parse attempt)
    if (globalProgressCallback) {
      parseProgressFromOutput(output, globalProgressCallback);
    }
  });

  child.stderr.on('data', (data) => {
    const lines = data.toString().split('\n').filter(line => line.trim());
    lines.forEach(line => {
      const trimmedLine = line.trim();

      // Only log important error messages, not every stderr line
      const shouldLogError =
        trimmedLine.includes('ERROR') ||
        trimmedLine.includes('CRITICAL') ||
        trimmedLine.includes('FATAL') ||
        trimmedLine.includes('Failed') ||
        trimmedLine.includes('Exception') ||
        trimmedLine.includes('Traceback') ||
        trimmedLine.includes('error while attempting to bind') ||
        trimmedLine.includes('Address already in use') ||
        trimmedLine.includes('Something went wrong');

      if (shouldLogError) {
        logTrellisServer('[Trellis Server] ' + trimmedLine);
        if (typeof logger !== 'undefined') logger.error('[Trellis Server] ' + trimmedLine);
      }

      // Check for port binding errors in stderr too
      if (trimmedLine.includes('error while attempting to bind') || trimmedLine.includes('Address already in use')) {
        logTrellisServer('PORT CONFLICT DETECTED - Another server is using port 7960');
        logTrellisServer('Please close any other Trellis servers and try again');
      }

      // Check for initialization failure in stderr and trigger automatic restart
      if (trimmedLine.includes('Something went wrong')) {
        if (recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
          logTrellisServer('🔄 Server initialization failed (stderr) - triggering automatic restart...');
          // Use setTimeout to avoid blocking the current process cleanup
          setTimeout(() => {
            triggerServerRestart(globalProgressCallback);
          }, 1000);
        } else {
          logTrellisServer('❌ Maximum recovery attempts reached, not restarting automatically');
        }
      }
    });
  });
  // Handle process events and log them to the main application logs
  child.on('close', (code) => {
    logTrellisServer('Process exited with code ' + code);
    if (typeof logger !== 'undefined') logger.info('Process exited with code ' + code);

    // If the process exits with a non-zero code during startup, it likely failed to initialize
    // This corresponds to the "Something went wrong" message in the batch file
    if (code !== 0 && code !== null) {
      if (recoveryAttemptCounter < MAX_RECOVERY_ATTEMPTS) {
        logTrellisServer('🔄 Server exited with error code ' + code + ' - triggering automatic restart...');
        // Use setTimeout to avoid blocking the current process cleanup
        setTimeout(() => {
          triggerServerRestart(globalProgressCallback);
        }, 2000);
      } else {
        logTrellisServer('❌ Maximum recovery attempts reached, not restarting automatically');
      }
    }
  });

  child.on('error', (error) => {
    logTrellisServer('Process error:', error);
    if (typeof logger !== 'undefined') logger.error('Process error: ' + error.message);
  });

  // Store the child process reference globally so we can check if it's still running
  global.trellisProcess = child;
}

async function waitForTrellisReady(timeoutMs = null, progressCb) {
  const start = Date.now();
  let waitCounter = 0;
  let recoveryTriggered = false;

  while (true) {
    if (await isTrellisRunning()) return true;

    waitCounter++;

    // Check if auto-recovery was triggered (connection counter was reset)
    if (connectionAttemptCounter < 10 && waitCounter > 150 && !recoveryTriggered) {
      logTrellisServer('🔄 Auto-recovery detected, resetting wait counter...');
      waitCounter = 0;
      recoveryTriggered = true;
    }

    // Only send progress callback every 5th check (every 10 seconds) to reduce spam
    if (progressCb && waitCounter % 5 === 0) {
      const message = recoveryTriggered ?
        'Server restarted, waiting for connection...' :
        'Waiting for Trellis server to start...';
      progressCb({ stage: 'trellis', message });
    }

    await new Promise(r => setTimeout(r, 2000));
    // No timeout: wait forever, but with auto-recovery
  }
}

// Global progress callback for server output parsing
let globalProgressCallback = null;

// Track current stage for simplified progress tracking
let currentStage = 'preprocessing';
let stageCompleted = {
  preprocessing: false,
  sparse_structure: false,
  slat_generation: false,
  mesh_creation: false,
  texture_generation: false,
  glb_export: false
};

// Counter for progress line logging to reduce spam
let progressLineCounter = 0;

// Helper function to determine if we should log this progress line
function shouldLogProgressLine(line, interval = 10) {
  progressLineCounter++;
  return progressLineCounter % interval === 0;
}

// Helper function to check if a line contains milestone progress (10%, 20%, 30%, etc.)
function isMilestoneProgress(line) {
  const percentageMatch = line.match(/(\d+)%/);
  if (percentageMatch) {
    const percentage = parseInt(percentageMatch[1]);
    // Log every 10% milestone, plus 0% and 100% for completeness
    return percentage % 10 === 0 || percentage === 0 || percentage === 100;
  }
  return false;
}



// Auto-recovery function to restart the server after failed attempts
async function triggerServerRestart(progressCb = null) {
  try {
    recoveryAttemptCounter++;
    logTrellisServer('🔧 Starting auto-recovery process (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')...');

    // Notify user of restart if progress callback is available
    if (progressCb) {
      progressCb({ stage: 'trellis', progress: 0, message: 'Server failed to start - restarting automatically...' });
    }

    // Step 1: Kill existing Python processes
    logTrellisServer('🧹 Cleaning up Python processes...');
    await cleanupPythonProcesses();

    // Step 2: Kill existing Trellis process if it exists
    if (global.trellisProcess) {
      logTrellisServer('🛑 Terminating existing Trellis process...');
      global.trellisProcess.kill('SIGTERM');
      global.trellisProcess = null;
    }

    // Step 3: Wait a moment for cleanup
    logTrellisServer('⏳ Waiting for cleanup to complete...');
    if (progressCb) {
      progressCb({ stage: 'trellis', progress: 25, message: 'Cleaning up processes...' });
    }
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Step 4: Reset connection counter and restart server
    connectionAttemptCounter = 0;
    logTrellisServer('🚀 Restarting Trellis server...');
    if (progressCb) {
      progressCb({ stage: 'trellis', progress: 50, message: 'Restarting server...' });
    }
    startTrellisServer(progressCb);

    logTrellisServer('✅ Auto-recovery process completed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + ')');

  } catch (error) {
    logTrellisServer('❌ Auto-recovery failed (attempt ' + recoveryAttemptCounter + '/' + MAX_RECOVERY_ATTEMPTS + '):', error);
    if (progressCb) {
      progressCb({ stage: 'trellis', progress: 0, message: 'Server restart failed - please try again' });
    }
  }
}

// Reset stage tracking for new generation
function resetStageTracking() {
  currentStage = 'preprocessing';
  stageCompleted = {
    preprocessing: false,
    sparse_structure: false,
    slat_generation: false,
    mesh_creation: false,
    texture_generation: false,
    glb_export: false
  };
  progressLineCounter = 0; // Reset progress line counter
  recoveryAttemptCounter = 0; // Reset recovery counter on successful new generation
  logTrellisServer('🔄 [Trellis Progress] Stage tracking reset for new generation');
  logTrellisServer('🎯 [Trellis Init] Starting fresh 3D generation pipeline');
}

function parseProgressFromOutput(output, progressCb) {
  if (!progressCb) {
    return;
  }

  const lines = output.split('\n');
  for (const line of lines) {
    try {
      const cleanLine = line.trim();
      if (!cleanLine) continue;

      // Skip ECONNREFUSED messages as they're not actual errors when server runs fine
      if (cleanLine.includes('ECONNREFUSED')) {
        continue;
      }

      // Enhanced logging: Log all significant progress-related lines for better visibility
      const isProgressLine = cleanLine.includes('Sampling:') || cleanLine.includes('Decimating') ||
                            cleanLine.includes('Rendering:') || cleanLine.includes('Rasterizing:') ||
                            cleanLine.includes('Texture baking') || cleanLine.includes('optimizing:') ||
                            cleanLine.includes('GLB saved') || cleanLine.includes('Model saved') ||
                            cleanLine.includes('Generation completed') || cleanLine.includes('Client asked to generate');

      if (isProgressLine) {
        logTrellisServer('🔍 [Trellis Raw Output]', cleanLine);
      }

      // PREPROCESSING STAGE - Complete when server is ready and processing starts
      if (!stageCompleted.preprocessing) {
        if (cleanLine.includes('Client asked to generate with no previews') ||
            cleanLine.includes('Sampling:') ||
            cleanLine.includes('Loading Trellis pipeline')) {
          logTrellisServer('✅ [Trellis Progress] Preprocessing: 100% → Starting sparse structure generation');
          logTrellisServer('🚀 [Trellis Stage] Moving to Sparse Structure Generation phase');
          stageCompleted.preprocessing = true;
          currentStage = 'sparse_structure';
          progressCb({ stage: 'preprocessing', progress: 100, step: 1, total: 1, message: 'Preprocessing completed' });
          progressCb({ stage: 'sparse_structure', progress: 0, step: 0, total: 12, message: 'Starting sparse structure generation...' });
        }
        continue;
      }

      // SPARSE STRUCTURE STAGE - Track progress and complete when finished
      if (!stageCompleted.sparse_structure && currentStage === 'sparse_structure') {
        // Parse sampling progress for sparse structure
        if (cleanLine.includes('Sampling:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/Sampling:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Enhanced logging: Log every 5% increment for better granularity
            if (percentage % 5 === 0) {
              logTrellisServer(`📊 [Trellis Progress] Sparse Structure: ${percentage}% - Building 3D structure from image`);
            }
            progressCb({ stage: 'sparse_structure', progress: percentage, step: Math.round(percentage * 12 / 100), total: 12, message: `Sparse structure: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Sampling: 100%|##########| 12/12')) {
          logTrellisServer('✅ [Trellis Progress] Sparse Structure: 100% → Starting SLAT generation');
          logTrellisServer('🔄 [Trellis Stage] Moving to SLAT Generation phase');
          stageCompleted.sparse_structure = true;
          currentStage = 'slat_generation';
          progressCb({ stage: 'sparse_structure', progress: 100, step: 12, total: 12, message: 'Sparse structure completed' });
          progressCb({ stage: 'slat_generation', progress: 0, step: 0, total: 12, message: 'Starting SLAT generation...' });
        }
        continue;
      }

      // SLAT GENERATION STAGE - Track progress and complete when finished
      if (!stageCompleted.slat_generation && currentStage === 'slat_generation') {
        // Parse sampling progress for SLAT generation
        if (cleanLine.includes('Sampling:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/Sampling:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Enhanced logging: Log every 5% increment for better granularity
            if (percentage % 5 === 0) {
              logTrellisServer(`📊 [Trellis Progress] SLAT Generation: ${percentage}% - Generating structured latent representation`);
            }
            progressCb({ stage: 'slat_generation', progress: percentage, step: Math.round(percentage * 12 / 100), total: 12, message: `SLAT generation: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Sampling: 100%|##########| 12/12') ||
            cleanLine.includes('Decoding the SLAT, please wait...')) {
          logTrellisServer('✅ [Trellis Progress] SLAT Generation: 100% → Starting mesh creation');
          logTrellisServer('🔧 [Trellis Stage] Moving to Mesh Creation phase');
          stageCompleted.slat_generation = true;
          currentStage = 'mesh_creation';
          progressCb({ stage: 'slat_generation', progress: 100, step: 12, total: 12, message: 'SLAT generation completed' });
          progressCb({ stage: 'mesh_creation', progress: 0, step: 0, total: 100, message: 'Starting mesh creation...' });
        }
        continue;
      }

      // MESH CREATION STAGE - Track progress and complete when finished
      if (!stageCompleted.mesh_creation && currentStage === 'mesh_creation') {
        // Parse decimation progress
        if (cleanLine.includes('Decimating Mesh:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/Decimating Mesh:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Enhanced logging: Log every 5% increment for better granularity
            if (percentage % 5 === 0) {
              logTrellisServer(`📊 [Trellis Progress] Mesh Decimation: ${percentage}% - Optimizing mesh geometry`);
            }
            progressCb({ stage: 'mesh_creation', progress: percentage, step: percentage, total: 100, message: `Mesh decimation: ${percentage}%` });
          }
        }

        // Parse rendering progress with enhanced logging
        if (cleanLine.includes('Rendering:') && cleanLine.includes('it [')) {
          const iterationMatch = cleanLine.match(/Rendering:\s*(\d+)it/);
          if (iterationMatch) {
            const iterations = parseInt(iterationMatch[1]);
            const percentage = Math.min(Math.floor((iterations / 100) * 100), 100);
            // Enhanced logging: Log every 5% increment for better granularity
            if (percentage % 5 === 0) {
              logTrellisServer(`📊 [Trellis Progress] Mesh Rendering: ${percentage}% - Rendering mesh details (${iterations} iterations)`);
            }
            progressCb({ stage: 'mesh_creation', progress: percentage, step: iterations, total: 100, message: `Mesh rendering: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Decimating Mesh: 100%|##########') ||
            cleanLine.includes('Rendering: 100it [')) {
          logTrellisServer('✅ [Trellis Progress] Mesh Creation: 100% → Starting texture generation');
          logTrellisServer('🎨 [Trellis Stage] Moving to Texture Generation phase');
          stageCompleted.mesh_creation = true;
          currentStage = 'texture_generation';
          progressCb({ stage: 'mesh_creation', progress: 100, step: 100, total: 100, message: 'Mesh creation completed' });
          progressCb({ stage: 'texture_generation', progress: 0, step: 0, total: 2500, message: 'Starting texture generation...' });
        }
        continue;
      }

      // TEXTURE GENERATION STAGE - Track progress and complete when finished
      if (!stageCompleted.texture_generation && currentStage === 'texture_generation') {
        // Parse texture baking progress
        if (cleanLine.includes('Texture baking (opt): optimizing:') && cleanLine.includes('%|')) {
          const percentageMatch = cleanLine.match(/optimizing:\s*(\d+)%/);
          if (percentageMatch) {
            const percentage = parseInt(percentageMatch[1]);
            // Enhanced logging: Log every 5% increment for better granularity
            if (percentage % 5 === 0) {
              logTrellisServer(`📊 [Trellis Progress] Texture Generation: ${percentage}% - Optimizing surface textures`);
            }
            progressCb({ stage: 'texture_generation', progress: percentage, step: Math.round(percentage * 2500 / 100), total: 2500, message: `Texture generation: ${percentage}%` });
          }
        }

        if (cleanLine.includes('Texture baking (opt): optimizing: 100%|##########| 2500/2500') ||
            cleanLine.includes('GLB saved to:') ||
            cleanLine.includes('Model saved to:')) {
          logTrellisServer('✅ [Trellis Progress] Texture Generation: 100% → Starting GLB export');
          logTrellisServer('📦 [Trellis Stage] Moving to GLB Export phase');
          stageCompleted.texture_generation = true;
          currentStage = 'glb_export';
          progressCb({ stage: 'texture_generation', progress: 100, step: 2500, total: 2500, message: 'Texture generation completed' });
          progressCb({ stage: 'glb_export', progress: 0, step: 0, total: 1, message: 'Starting GLB export...' });
        }
        continue;
      }

      // GLB EXPORT STAGE - Complete when model is fully exported
      if (!stageCompleted.glb_export && currentStage === 'glb_export') {
        if (cleanLine.includes('Generation completed') ||
            cleanLine.includes('Model generation completed') ||
            cleanLine.includes('Videos rendered successfully')) {
          logTrellisServer('🎉 [Trellis Progress] GLB Export: 100% → Generation finished!');
          logTrellisServer('✨ [Trellis Complete] 3D model generation successfully completed');
          stageCompleted.glb_export = true;
          progressCb({ stage: 'glb_export', progress: 100, step: 1, total: 1, message: 'GLB export completed' });
        }
        continue;
      }

    } catch (error) {
      logTrellisServer('❌ [Trellis Error] Error in progress tracking:', error);
    }
  }
}

function mapTrellisMessageToStage(message, progress) {
  // Simple stage mapping based on progress percentage
  // This matches the ProgressBar component's expected stages

  if (progress <= 0) {
    return { stage: 'preprocessing', description: 'Loading image and initializing pipeline' };
  } else if (progress <= 10) {
    return { stage: 'sparse_structure', description: 'Generating 3D structure foundation' };
  } else if (progress <= 50) {
    return { stage: 'slat_generation', description: 'Creating detailed 3D representation' };
  } else if (progress <= 60) {
    return { stage: 'mesh_creation', description: 'Processing and optimizing mesh' };
  } else if (progress <= 95) {
    return { stage: 'glb_export', description: 'Finalizing and exporting 3D model' };
  } else {
    return { stage: 'download', description: 'Downloading generated 3D model' };
  }
}

async function generate3DModel(imagePath, progressCb, settings = {}) {
  logTrellisServer('🚀 [Trellis Main] generate3DModel called with imagePath:', imagePath);
  logTrellisServer('🚀 [Trellis Settings] Settings received:', settings);
  logTrellisServer('🧹 [Trellis Init] Preparing environment for 3D generation');

  // First, cleanup any lingering Python processes to prevent permission errors
  await cleanupPythonProcesses();

  const isRunning = await isTrellisRunning();
  // Reset stage tracking for new generation
  resetStageTracking();

  // Set the global progress callback for server output parsing
  globalProgressCallback = progressCb;

  if (!isRunning) {
    logTrellisServer('🔧 [Trellis Server] Server not running, starting Trellis server...');
    if (progressCb) {
      const { stage, description } = mapTrellisMessageToStage('Starting Trellis server...', 0);
      progressCb({ stage, progress: 0, message: description });
    }
    try {
      startTrellisServer(progressCb);
      logTrellisServer('✅ [Trellis Server] startTrellisServer() called successfully');
    } catch (error) {
      logTrellisServer('❌ [Trellis Server] Error starting server:', error);
      throw error;
    }
    await waitForTrellisReady(null, progressCb);
  } else {
    logTrellisServer('✅ [Trellis Server] Server already running, skipping startup');
  }

  logTrellisServer('📤 [Trellis Request] Preparing to send image to Trellis for 3D generation');
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Sending image to Trellis for 3D generation...', 0);
    progressCb({ stage, progress: 0, step: 0, total: 1, message: description });
  }
  const form = new FormData();
  form.append('file', fs.createReadStream(imagePath), { filename: path.basename(imagePath) });

  // Add settings as form data
  if (settings.seed !== undefined) form.append('seed', settings.seed.toString());
  if (settings.ss_cfg_strength !== undefined) form.append('ss_guidance_strength', settings.ss_cfg_strength.toString());
  if (settings.ss_steps !== undefined) form.append('ss_sampling_steps', settings.ss_steps.toString());
  if (settings.slat_cfg_strength !== undefined) form.append('slat_guidance_strength', settings.slat_cfg_strength.toString());
  if (settings.slat_steps !== undefined) form.append('slat_sampling_steps', settings.slat_steps.toString());
  if (settings.simplify !== undefined) form.append('mesh_simplify_ratio', settings.simplify.toString());
  if (settings.texture_size !== undefined) form.append('texture_size', settings.texture_size.toString());
  if (settings.use_hunyuan_texturing !== undefined) form.append('use_hunyuan_texturing', settings.use_hunyuan_texturing.toString());
  if (settings.use_hunyuan_lighting !== undefined) form.append('use_hunyuan_lighting', settings.use_hunyuan_lighting.toString());

  logTrellisServer('📤 [Trellis Settings] Form data prepared with settings:', {
    seed: settings.seed,
    ss_cfg_strength: settings.ss_cfg_strength,
    ss_steps: settings.ss_steps,
    slat_cfg_strength: settings.slat_cfg_strength,
    slat_steps: settings.slat_steps,
    simplify: settings.simplify,
    texture_size: settings.texture_size,
    use_hunyuan_texturing: settings.use_hunyuan_texturing,
    use_hunyuan_lighting: settings.use_hunyuan_lighting
  });

  let response;
  try {
    logTrellisServer('🚀 [Trellis Request] Sending POST request to:', 'http://' + TRELLIS_HOST + ':' + TRELLIS_PORT + '/generate_no_preview');
    response = await axios.post(
      'http://' + TRELLIS_HOST + ':' + TRELLIS_PORT + '/generate_no_preview',
      form,
      {
        headers: form.getHeaders(),
        maxBodyLength: Infinity
        // No timeout - let it run as long as needed
      }
    );
    logTrellisServer('POST response status:', response.status);
    logTrellisServer('POST response data:', response.data);
  } catch (err) {
    logTrellisServer('POST request failed:', err.response?.status, err.response?.data);
    logTrellisServer('Full error:', err.message);
    throw new Error('Failed to POST to Trellis: ' + (err.response?.data?.message || err.message));
  }

  // After POST, poll /progress in real time
  let done = false;
  while (!done) {
    try {
      const res = await axios.get('http://127.0.0.1:7960/progress');
      const data = res.data;
      // Forward progress to renderer via progressCb
      if (progressCb) {
        progressCb({
          event: 'progress',
          stage: mapTrellisMessageToStage(data.message),
          progress: data.progress,
          message: data.message,
          status: data.status
        });
      }
      // Also send IPC event if needed
      // sendProgressToRenderer({
      //   event: 'progress',
      //   stage: mapTrellisMessageToStage(data.message),
      //   progress: data.progress,
      //   message: data.message,
      //   status: data.status
      // });
      if (data.progress >= 100 || data.status === 'COMPLETED' || data.status === 'FAILED') {
        done = true;
      } else {
        await new Promise(r => setTimeout(r, 500));
      }
    } catch (err) {
      logTrellisServer('[Trellis Progress] Error polling /progress:', err);
      await new Promise(r => setTimeout(r, 1000));
    }
  }

  // Download model as before
  let modelUrl = response.data.model_url;
  if (!modelUrl) throw new Error('No model_url in Trellis response');
  const fullModelUrl = modelUrl.startsWith('http') ? modelUrl : 'http://' + TRELLIS_HOST + ':' + TRELLIS_PORT + modelUrl;
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('Downloading generated 3D model...', 100);
    progressCb({ stage, progress: 100, message: description });
  }
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  const modelResponse = await axios.get(fullModelUrl, { responseType: 'stream' });
  let fileName = path.basename(fullModelUrl.split('?')[0]) || 'model';
  if (!fileName.endsWith('.glb') && !fileName.endsWith('.gltf')) {
    fileName = fileName + '.glb';
  }
  const outputPath = path.join(OUTPUT_DIR, fileName);
  await new Promise((resolve, reject) => {
    const writer = fs.createWriteStream(outputPath);
    modelResponse.data.pipe(writer);
    writer.on('finish', () => {
      logTrellisServer('💾 [Trellis Complete] Model file saved successfully');
      logTrellisServer('🎉 [Trellis Success] 3D generation pipeline completed successfully!');
      resolve();
    });
    writer.on('error', (error) => {
      logTrellisServer('❌ [Trellis Error] Error saving model file:', error);
      reject(error);
    });
  });
  if (progressCb) {
    const { stage, description } = mapTrellisMessageToStage('3D model downloaded.', 100);
    progressCb({ stage, progress: 100, message: description });
  }
  const app = require('electron').app;
  const relativePath = path.relative(app.getAppPath(), outputPath);
  logTrellisServer('📁 [Trellis Output] Model saved to:', relativePath);
  return relativePath;
}

module.exports = {
  generate3DModel,
  cleanupPythonProcesses
};